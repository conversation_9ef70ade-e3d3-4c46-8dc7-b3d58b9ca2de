import React, { useState, useEffect, useCallback } from 'react';
import { 
  ChevronRightIcon, 
  ChevronDownIcon,
  UserIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import Loading from '@/components/common/Loading';
import Badge from '@/components/common/Badge';
import { useApi } from '@/hooks/useApi';
import { financialService } from '@/services/financial';
import { 
  HierarchicalTransactionData, 
  HierarchicalTransactionParams,
  DateTransactionGroup,
  UserTransactionGroup,
  TypeTransactionGroup,
  TransactionType
} from '@/types/financial';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const HierarchicalTransactionView: React.FC = () => {
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalTransactionData | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<HierarchicalTransactionParams>({
    groupBy: 'date',
    page: 1,
    limit: 50
  });

  // API调用
  const {
    loading,
    execute: fetchHierarchicalData,
  } = useApi(financialService.getHierarchicalTransactions);

  // 加载分层级数据
  const loadHierarchicalData = useCallback(async () => {
    try {
      const result = await fetchHierarchicalData(filters);
      setHierarchicalData(result);
    } catch (error) {
      console.error('加载分层级交易数据失败:', error);
    }
  }, [fetchHierarchicalData, filters]);

  // 初始加载
  useEffect(() => {
    loadHierarchicalData();
  }, [loadHierarchicalData]);

  // 切换展开/折叠状态
  const toggleExpansion = (key: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedItems(newExpanded);
  };

  // 格式化金额
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // 获取交易类型样式
  const getTypeStyle = (type: TransactionType) => {
    switch (type) {
      case TransactionType.Deposit:
        return 'bg-green-100 text-green-800';
      case TransactionType.In:
        return 'bg-blue-100 text-blue-800';
      case TransactionType.Out:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 渲染按日期分组的视图
  const renderDateGroupView = () => {
    const dateGroups = hierarchicalData?.hierarchicalData as DateTransactionGroup[];
    
    return dateGroups?.map((dateGroup) => (
      <div key={dateGroup.date} className="border border-gray-200 rounded-lg mb-4">
        {/* 日期组头部 */}
        <div 
          className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
          onClick={() => toggleExpansion(`date-${dateGroup.date}`)}
        >
          <div className="flex items-center space-x-3">
            {expandedItems.has(`date-${dateGroup.date}`) ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            )}
            <CalendarIcon className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {format(new Date(dateGroup.date), 'yyyy年MM月dd日', { locale: zhCN })}
              </h3>
              <p className="text-sm text-gray-500">
                {dateGroup.totalCount} 笔交易 • 总金额 {formatAmount(dateGroup.totalAmount)}
              </p>
            </div>
          </div>
          <Badge variant="secondary">
            {dateGroup.users?.length || 0} 个用户
          </Badge>
        </div>

        {/* 用户列表 */}
        {expandedItems.has(`date-${dateGroup.date}`) && (
          <div className="divide-y divide-gray-200">
            {dateGroup.users?.map((userGroup) => (
              <div key={userGroup.user._id} className="p-4">
                <div 
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded"
                  onClick={() => toggleExpansion(`user-${dateGroup.date}-${userGroup.user._id}`)}
                >
                  <div className="flex items-center space-x-3">
                    {expandedItems.has(`user-${dateGroup.date}-${userGroup.user._id}`) ? (
                      <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {userGroup.user.fullName || userGroup.user.email}
                      </p>
                      <p className="text-sm text-gray-500">
                        {userGroup.totalCount} 笔交易 • {formatAmount(userGroup.totalAmount)}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {userGroup.typeStats.deposit > 0 && (
                      <Badge className="bg-green-100 text-green-800" size="sm">
                        充值 {formatAmount(userGroup.typeStats.deposit)}
                      </Badge>
                    )}
                    {userGroup.typeStats.in > 0 && (
                      <Badge className="bg-blue-100 text-blue-800" size="sm">
                        入账 {formatAmount(userGroup.typeStats.in)}
                      </Badge>
                    )}
                    {userGroup.typeStats.out > 0 && (
                      <Badge className="bg-red-100 text-red-800" size="sm">
                        消费 {formatAmount(userGroup.typeStats.out)}
                      </Badge>
                    )}
                  </div>
                </div>

                {/* 交易详情 */}
                {expandedItems.has(`user-${dateGroup.date}-${userGroup.user._id}`) && (
                  <div className="mt-3 ml-8 space-y-2">
                    {userGroup.transactions.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center space-x-3">
                          <Badge className={getTypeStyle(transaction.type)} size="sm">
                            {transaction.typeLabel}
                          </Badge>
                          <span className="text-sm text-gray-600">{transaction.reason}</span>
                          {transaction.pricing && (
                            <span className="text-xs text-gray-500">
                              {transaction.pricing.modelName}
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === TransactionType.Out ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === TransactionType.Out ? '-' : '+'}
                            {formatAmount(Math.abs(transaction.amount))}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(transaction.timestamp), 'HH:mm:ss')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  // 渲染按用户分组的视图
  const renderUserGroupView = () => {
    const userGroups = hierarchicalData?.hierarchicalData as UserTransactionGroup[];
    
    return userGroups?.map((userGroup) => (
      <div key={userGroup.user._id} className="border border-gray-200 rounded-lg mb-4">
        {/* 用户组头部 */}
        <div 
          className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
          onClick={() => toggleExpansion(`user-${userGroup.user._id}`)}
        >
          <div className="flex items-center space-x-3">
            {expandedItems.has(`user-${userGroup.user._id}`) ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            )}
            <UserIcon className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {userGroup.user.fullName || userGroup.user.email}
              </h3>
              <p className="text-sm text-gray-500">
                {userGroup.totalCount} 笔交易 • 总金额 {formatAmount(userGroup.totalAmount)}
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            {userGroup.typeStats.deposit > 0 && (
              <Badge className="bg-green-100 text-green-800" size="sm">
                充值 {formatAmount(userGroup.typeStats.deposit)}
              </Badge>
            )}
            {userGroup.typeStats.in > 0 && (
              <Badge className="bg-blue-100 text-blue-800" size="sm">
                入账 {formatAmount(userGroup.typeStats.in)}
              </Badge>
            )}
            {userGroup.typeStats.out > 0 && (
              <Badge className="bg-red-100 text-red-800" size="sm">
                消费 {formatAmount(userGroup.typeStats.out)}
              </Badge>
            )}
          </div>
        </div>

        {/* 日期分组 */}
        {expandedItems.has(`user-${userGroup.user._id}`) && (
          <div className="divide-y divide-gray-200">
            {userGroup.dateGroups?.map((dateGroup) => (
              <div key={dateGroup.date} className="p-4">
                <div 
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded"
                  onClick={() => toggleExpansion(`date-${userGroup.user._id}-${dateGroup.date}`)}
                >
                  <div className="flex items-center space-x-3">
                    {expandedItems.has(`date-${userGroup.user._id}-${dateGroup.date}`) ? (
                      <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {format(new Date(dateGroup.date), 'MM月dd日', { locale: zhCN })}
                      </p>
                      <p className="text-sm text-gray-500">
                        {dateGroup.count} 笔交易 • {formatAmount(dateGroup.totalAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 交易详情 */}
                {expandedItems.has(`date-${userGroup.user._id}-${dateGroup.date}`) && (
                  <div className="mt-3 ml-8 space-y-2">
                    {dateGroup.transactions?.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center space-x-3">
                          <Badge className={getTypeStyle(transaction.type)} size="sm">
                            {transaction.typeLabel}
                          </Badge>
                          <span className="text-sm text-gray-600">{transaction.reason}</span>
                          {transaction.pricing && (
                            <span className="text-xs text-gray-500">
                              {transaction.pricing.modelName}
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === TransactionType.Out ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === TransactionType.Out ? '-' : '+'}
                            {formatAmount(Math.abs(transaction.amount))}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(transaction.timestamp), 'HH:mm:ss')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  // 渲染按类型分组的视图
  const renderTypeGroupView = () => {
    const typeGroups = hierarchicalData?.hierarchicalData as TypeTransactionGroup[];

    return typeGroups?.map((typeGroup) => (
      <div key={typeGroup.type} className="border border-gray-200 rounded-lg mb-4">
        {/* 类型组头部 */}
        <div
          className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
          onClick={() => toggleExpansion(`type-${typeGroup.type}`)}
        >
          <div className="flex items-center space-x-3">
            {expandedItems.has(`type-${typeGroup.type}`) ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            )}
            <CurrencyDollarIcon className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {typeGroup.typeLabel}
              </h3>
              <p className="text-sm text-gray-500">
                {typeGroup.totalCount} 笔交易 • 总金额 {formatAmount(typeGroup.totalAmount)}
              </p>
            </div>
          </div>
          <Badge className={getTypeStyle(typeGroup.type)}>
            {typeGroup.users?.length || 0} 个用户
          </Badge>
        </div>

        {/* 用户列表 */}
        {expandedItems.has(`type-${typeGroup.type}`) && (
          <div className="divide-y divide-gray-200">
            {typeGroup.users?.map((userGroup) => (
              <div key={userGroup.user._id} className="p-4">
                <div
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded"
                  onClick={() => toggleExpansion(`user-${typeGroup.type}-${userGroup.user._id}`)}
                >
                  <div className="flex items-center space-x-3">
                    {expandedItems.has(`user-${typeGroup.type}-${userGroup.user._id}`) ? (
                      <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {userGroup.user.fullName || userGroup.user.email}
                      </p>
                      <p className="text-sm text-gray-500">
                        {userGroup.totalCount} 笔交易 • {formatAmount(userGroup.totalAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 交易详情 */}
                {expandedItems.has(`user-${typeGroup.type}-${userGroup.user._id}`) && (
                  <div className="mt-3 ml-8 space-y-2">
                    {userGroup.transactions.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-600">{transaction.reason}</span>
                          {transaction.pricing && (
                            <span className="text-xs text-gray-500">
                              {transaction.pricing.modelName}
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === TransactionType.Out ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === TransactionType.Out ? '-' : '+'}
                            {formatAmount(Math.abs(transaction.amount))}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(transaction.timestamp), 'MM-dd HH:mm')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">分层级交易记录</h1>
          <p className="mt-1 text-sm text-gray-600">
            按不同维度查看交易记录的层级结构
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant={filters.groupBy === 'date' ? 'primary' : 'secondary'}
            onClick={() => setFilters(prev => ({ ...prev, groupBy: 'date' }))}
          >
            按日期分组
          </Button>
          <Button 
            variant={filters.groupBy === 'user' ? 'primary' : 'secondary'}
            onClick={() => setFilters(prev => ({ ...prev, groupBy: 'user' }))}
          >
            按用户分组
          </Button>
          <Button 
            variant={filters.groupBy === 'type' ? 'primary' : 'secondary'}
            onClick={() => setFilters(prev => ({ ...prev, groupBy: 'type' }))}
          >
            按类型分组
          </Button>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <FunnelIcon className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">筛选条件</span>
          {/* 这里可以添加更多筛选器 */}
        </div>
      </div>

      {/* 分层级数据展示 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            交易记录层级视图 - {filters.groupBy === 'date' ? '按日期分组' : filters.groupBy === 'user' ? '按用户分组' : '按类型分组'}
          </h3>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex justify-center py-8">
              <Loading />
            </div>
          ) : !hierarchicalData?.hierarchicalData.length ? (
            <div className="text-center py-8 text-gray-500">
              暂无交易数据
            </div>
          ) : (
            <div className="space-y-4">
              {filters.groupBy === 'date' && renderDateGroupView()}
              {filters.groupBy === 'user' && renderUserGroupView()}
              {filters.groupBy === 'type' && renderTypeGroupView()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HierarchicalTransactionView;
