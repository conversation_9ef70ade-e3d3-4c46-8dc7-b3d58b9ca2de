import React, { useState, useEffect, useCallback } from 'react';
import {
  ChevronRightIcon,
  ChevronDownIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  EnvelopeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import Loading from '@/components/common/Loading';
import Badge from '@/components/common/Badge';
import { useApi } from '@/hooks/useApi';
import { financialService } from '@/services/financial';
import {
  BalanceTransaction,
  TransactionType
} from '@/types/financial';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 用户交易分组接口
interface UserTransactionGroup {
  user: {
    _id: string;
    email: string;
    fullName?: string;
    balance?: number;
  };
  sessions: SessionTransactionGroup[];
  totalTransactions: number;
  totalAmount: number;
  typeStats: {
    deposit: number;
    in: number;
    out: number;
  };
  isExpanded: boolean;
  isLoading: boolean;
}

// 会话交易分组接口
interface SessionTransactionGroup {
  sessionId: string;
  sessionTitle?: string;
  transactions: BalanceTransaction[];
  totalAmount: number;
  totalCount: number;
  firstTransactionTime: string;
  lastTransactionTime: string;
  isExpanded: boolean;
  isLoading: boolean;
}

// 分层级数据接口
interface HierarchicalTransactionData {
  users: UserTransactionGroup[];
  totalUsers: number;
  totalSessions: number;
  totalTransactions: number;
  totalAmount: number;
}

const HierarchicalTransactionView: React.FC = () => {
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalTransactionData>({
    users: [],
    totalUsers: 0,
    totalSessions: 0,
    totalTransactions: 0,
    totalAmount: 0
  });

  // API调用
  const {
    loading: loadingUsers,
    execute: fetchTransactions,
  } = useApi(financialService.getTransactions);

  // 加载用户交易数据
  const loadUserTransactions = useCallback(async () => {
    try {
      const result = await fetchTransactions({
        page: 1,
        limit: 100, // 受后端验证限制，最大为100
        sortBy: 'timestamp',
        sortOrder: 'desc'
      });

      // 按用户分组交易数据
      const userGroups = new Map<string, UserTransactionGroup>();

      result.items.forEach((transaction: BalanceTransaction) => {
        const userId = transaction.userId;
        const userEmail = transaction.user?.email || '未知用户';

        if (!userGroups.has(userId)) {
          userGroups.set(userId, {
            user: {
              _id: userId,
              email: userEmail,
              fullName: transaction.user?.fullName,
              balance: transaction.user?.balance
            },
            sessions: [],
            totalTransactions: 0,
            totalAmount: 0,
            typeStats: { deposit: 0, in: 0, out: 0 },
            isExpanded: false,
            isLoading: false
          });
        }

        const userGroup = userGroups.get(userId)!;
        userGroup.totalTransactions += 1;
        userGroup.totalAmount += Math.abs(transaction.amount);

        // 统计交易类型
        switch (transaction.type) {
          case TransactionType.Deposit:
            userGroup.typeStats.deposit += transaction.amount;
            break;
          case TransactionType.In:
            userGroup.typeStats.in += transaction.amount;
            break;
          case TransactionType.Out:
            userGroup.typeStats.out += Math.abs(transaction.amount);
            break;
        }

        // 按会话分组（这里使用 reason 作为会话标识，实际项目中可能需要调整）
        const sessionId = transaction.reason || 'default';
        let sessionGroup = userGroup.sessions.find(s => s.sessionId === sessionId);

        if (!sessionGroup) {
          sessionGroup = {
            sessionId,
            sessionTitle: transaction.reason || '默认会话',
            transactions: [],
            totalAmount: 0,
            totalCount: 0,
            firstTransactionTime: transaction.timestamp,
            lastTransactionTime: transaction.timestamp,
            isExpanded: false,
            isLoading: false
          };
          userGroup.sessions.push(sessionGroup);
        }

        sessionGroup.transactions.push(transaction);
        sessionGroup.totalAmount += Math.abs(transaction.amount);
        sessionGroup.totalCount += 1;

        // 更新时间范围
        if (new Date(transaction.timestamp) < new Date(sessionGroup.firstTransactionTime)) {
          sessionGroup.firstTransactionTime = transaction.timestamp;
        }
        if (new Date(transaction.timestamp) > new Date(sessionGroup.lastTransactionTime)) {
          sessionGroup.lastTransactionTime = transaction.timestamp;
        }
      });

      // 对会话按最后交易时间排序
      userGroups.forEach(userGroup => {
        userGroup.sessions.sort((a, b) =>
          new Date(b.lastTransactionTime).getTime() - new Date(a.lastTransactionTime).getTime()
        );
      });

      const users = Array.from(userGroups.values());

      setHierarchicalData({
        users,
        totalUsers: users.length,
        totalSessions: users.reduce((sum, user) => sum + user.sessions.length, 0),
        totalTransactions: result.total,
        totalAmount: users.reduce((sum, user) => sum + user.totalAmount, 0)
      });
    } catch (error) {
      console.error('加载用户交易数据失败:', error);
    }
  }, [fetchTransactions]);

  // 初始加载
  useEffect(() => {
    loadUserTransactions();
  }, [loadUserTransactions]);

  // 切换用户展开状态
  const toggleUserExpansion = useCallback((userId: string) => {
    setHierarchicalData(prev => ({
      ...prev,
      users: prev.users.map(userGroup =>
        userGroup.user._id === userId
          ? { ...userGroup, isExpanded: !userGroup.isExpanded }
          : userGroup
      )
    }));
  }, []);

  // 切换会话展开状态
  const toggleSessionExpansion = useCallback((userId: string, sessionId: string) => {
    setHierarchicalData(prev => ({
      ...prev,
      users: prev.users.map(userGroup =>
        userGroup.user._id === userId
          ? {
              ...userGroup,
              sessions: userGroup.sessions.map(sessionGroup =>
                sessionGroup.sessionId === sessionId
                  ? { ...sessionGroup, isExpanded: !sessionGroup.isExpanded }
                  : sessionGroup
              )
            }
          : userGroup
      )
    }));
  }, []);

  // 格式化金额
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // 获取交易类型样式
  const getTypeStyle = (type: TransactionType) => {
    switch (type) {
      case TransactionType.Deposit:
        return 'bg-green-100 text-green-800';
      case TransactionType.In:
        return 'bg-blue-100 text-blue-800';
      case TransactionType.Out:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取用户角色样式
  const getUserRoleStyle = (email: string) => {
    if (email.includes('admin')) {
      return 'bg-red-100 text-red-800';
    } else if (email.includes('premium')) {
      return 'bg-purple-100 text-purple-800';
    }
    return 'bg-blue-100 text-blue-800';
  };



  // 渲染按用户分组的视图
  const renderUserGroupView = () => {
    const userGroups = hierarchicalData?.hierarchicalData as UserTransactionGroup[];
    
    return userGroups?.map((userGroup) => (
      <div key={userGroup.user._id} className="border border-gray-200 rounded-lg mb-4">
        {/* 用户组头部 */}
        <div 
          className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
          onClick={() => toggleExpansion(`user-${userGroup.user._id}`)}
        >
          <div className="flex items-center space-x-3">
            {expandedItems.has(`user-${userGroup.user._id}`) ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            )}
            <UserIcon className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {userGroup.user.fullName || userGroup.user.email}
              </h3>
              <p className="text-sm text-gray-500">
                {userGroup.totalCount} 笔交易 • 总金额 {formatAmount(userGroup.totalAmount)}
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            {userGroup.typeStats.deposit > 0 && (
              <Badge className="bg-green-100 text-green-800" size="sm">
                充值 {formatAmount(userGroup.typeStats.deposit)}
              </Badge>
            )}
            {userGroup.typeStats.in > 0 && (
              <Badge className="bg-blue-100 text-blue-800" size="sm">
                入账 {formatAmount(userGroup.typeStats.in)}
              </Badge>
            )}
            {userGroup.typeStats.out > 0 && (
              <Badge className="bg-red-100 text-red-800" size="sm">
                消费 {formatAmount(userGroup.typeStats.out)}
              </Badge>
            )}
          </div>
        </div>

        {/* 日期分组 */}
        {expandedItems.has(`user-${userGroup.user._id}`) && (
          <div className="divide-y divide-gray-200">
            {userGroup.dateGroups?.map((dateGroup) => (
              <div key={dateGroup.date} className="p-4">
                <div 
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded"
                  onClick={() => toggleExpansion(`date-${userGroup.user._id}-${dateGroup.date}`)}
                >
                  <div className="flex items-center space-x-3">
                    {expandedItems.has(`date-${userGroup.user._id}-${dateGroup.date}`) ? (
                      <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {format(new Date(dateGroup.date), 'MM月dd日', { locale: zhCN })}
                      </p>
                      <p className="text-sm text-gray-500">
                        {dateGroup.count} 笔交易 • {formatAmount(dateGroup.totalAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 交易详情 */}
                {expandedItems.has(`date-${userGroup.user._id}-${dateGroup.date}`) && (
                  <div className="mt-3 ml-8 space-y-2">
                    {dateGroup.transactions?.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center space-x-3">
                          <Badge className={getTypeStyle(transaction.type)} size="sm">
                            {transaction.typeLabel}
                          </Badge>
                          <span className="text-sm text-gray-600">{transaction.reason}</span>
                          {transaction.pricing && (
                            <span className="text-xs text-gray-500">
                              {transaction.pricing.modelName}
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === TransactionType.Out ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === TransactionType.Out ? '-' : '+'}
                            {formatAmount(Math.abs(transaction.amount))}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(transaction.timestamp), 'HH:mm:ss')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  // 渲染按类型分组的视图
  const renderTypeGroupView = () => {
    const typeGroups = hierarchicalData?.hierarchicalData as TypeTransactionGroup[];

    return typeGroups?.map((typeGroup) => (
      <div key={typeGroup.type} className="border border-gray-200 rounded-lg mb-4">
        {/* 类型组头部 */}
        <div
          className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
          onClick={() => toggleExpansion(`type-${typeGroup.type}`)}
        >
          <div className="flex items-center space-x-3">
            {expandedItems.has(`type-${typeGroup.type}`) ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            )}
            <CurrencyDollarIcon className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {typeGroup.typeLabel}
              </h3>
              <p className="text-sm text-gray-500">
                {typeGroup.totalCount} 笔交易 • 总金额 {formatAmount(typeGroup.totalAmount)}
              </p>
            </div>
          </div>
          <Badge className={getTypeStyle(typeGroup.type)}>
            {typeGroup.users?.length || 0} 个用户
          </Badge>
        </div>

        {/* 用户列表 */}
        {expandedItems.has(`type-${typeGroup.type}`) && (
          <div className="divide-y divide-gray-200">
            {typeGroup.users?.map((userGroup) => (
              <div key={userGroup.user._id} className="p-4">
                <div
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded"
                  onClick={() => toggleExpansion(`user-${typeGroup.type}-${userGroup.user._id}`)}
                >
                  <div className="flex items-center space-x-3">
                    {expandedItems.has(`user-${typeGroup.type}-${userGroup.user._id}`) ? (
                      <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {userGroup.user.fullName || userGroup.user.email}
                      </p>
                      <p className="text-sm text-gray-500">
                        {userGroup.totalCount} 笔交易 • {formatAmount(userGroup.totalAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 交易详情 */}
                {expandedItems.has(`user-${typeGroup.type}-${userGroup.user._id}`) && (
                  <div className="mt-3 ml-8 space-y-2">
                    {userGroup.transactions.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-600">{transaction.reason}</span>
                          {transaction.pricing && (
                            <span className="text-xs text-gray-500">
                              {transaction.pricing.modelName}
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === TransactionType.Out ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === TransactionType.Out ? '-' : '+'}
                            {formatAmount(Math.abs(transaction.amount))}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(transaction.timestamp), 'MM-dd HH:mm')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和统计 */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">交易记录分层级视图</h1>
          <p className="mt-1 text-sm text-gray-600">
            按用户邮箱和会话分层级管理交易记录
          </p>
          <div className="mt-3 flex space-x-6 text-sm text-gray-500">
            <span>用户: {hierarchicalData.totalUsers}</span>
            <span>会话: {hierarchicalData.totalSessions}</span>
            <span>交易: {hierarchicalData.totalTransactions}</span>
            <span>总金额: {formatAmount(hierarchicalData.totalAmount)}</span>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={loadUserTransactions}
            loading={loadingUsers}
            size="sm"
          >
            刷新数据
          </Button>
        </div>
      </div>

      {/* 层级数据展示 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            交易记录层级视图
          </h3>
        </div>

        <div className="divide-y divide-gray-200">
          {loadingUsers ? (
            <div className="p-8">
              <Loading />
            </div>
          ) : hierarchicalData.users.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              暂无交易数据
            </div>
          ) : (
            hierarchicalData.users.map((userGroup) => (
              <div key={userGroup.user._id} className="border-l-4 border-l-blue-500">
                {/* 用户级别 */}
                <div
                  className="p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                  onClick={() => toggleUserExpansion(userGroup.user._id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {/* 展开/折叠图标 */}
                      <div className="flex-shrink-0">
                        {userGroup.isExpanded ? (
                          <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </div>

                      {/* 用户头像 */}
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-500" />
                        </div>
                      </div>

                      {/* 用户信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {userGroup.user.fullName || '未知用户'}
                          </h3>
                          <Badge
                            variant="secondary"
                            className={getUserRoleStyle(userGroup.user.email)}
                            size="sm"
                          >
                            用户
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <div className="flex items-center space-x-1">
                            <EnvelopeIcon className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-500">{userGroup.user.email}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <ChatBubbleLeftRightIcon className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-500">{userGroup.sessions.length} 个会话</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-500">{userGroup.totalTransactions} 笔交易</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="flex space-x-2">
                      {userGroup.typeStats.deposit > 0 && (
                        <Badge className="bg-green-100 text-green-800" size="sm">
                          充值 {formatAmount(userGroup.typeStats.deposit)}
                        </Badge>
                      )}
                      {userGroup.typeStats.in > 0 && (
                        <Badge className="bg-blue-100 text-blue-800" size="sm">
                          入账 {formatAmount(userGroup.typeStats.in)}
                        </Badge>
                      )}
                      {userGroup.typeStats.out > 0 && (
                        <Badge className="bg-red-100 text-red-800" size="sm">
                          消费 {formatAmount(userGroup.typeStats.out)}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* 会话列表 */}
                {userGroup.isExpanded && (
                  <div className="bg-gray-50 border-t border-gray-200">
                    {userGroup.sessions.length === 0 ? (
                      <div className="p-8 text-center text-gray-500">
                        该用户暂无会话记录
                      </div>
                    ) : (
                      <div className="divide-y divide-gray-200">
                        {userGroup.sessions.map((sessionGroup) => (
                          <div key={sessionGroup.sessionId} className="p-4">
                            <div
                              className="flex items-center justify-between cursor-pointer hover:bg-gray-100 p-2 rounded"
                              onClick={() => toggleSessionExpansion(userGroup.user._id, sessionGroup.sessionId)}
                            >
                              <div className="flex items-center space-x-3">
                                {sessionGroup.isExpanded ? (
                                  <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                                ) : (
                                  <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                                )}
                                <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400" />
                                <div>
                                  <p className="font-medium text-gray-900">
                                    {sessionGroup.sessionTitle}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {sessionGroup.totalCount} 笔交易 • {formatAmount(sessionGroup.totalAmount)}
                                  </p>
                                  <p className="text-xs text-gray-400">
                                    {format(new Date(sessionGroup.firstTransactionTime), 'yyyy-MM-dd HH:mm')} - {format(new Date(sessionGroup.lastTransactionTime), 'yyyy-MM-dd HH:mm')}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* 交易详情 */}
                            {sessionGroup.isExpanded && (
                              <div className="mt-3 ml-8 space-y-2">
                                {sessionGroup.transactions.map((transaction) => (
                                  <div key={transaction._id} className="flex items-center justify-between p-3 bg-white rounded border">
                                    <div className="flex items-center space-x-3">
                                      <Badge className={getTypeStyle(transaction.type)} size="sm">
                                        {transaction.type === TransactionType.Deposit ? '充值' :
                                         transaction.type === TransactionType.In ? '入账' : '消费'}
                                      </Badge>
                                      <span className="text-sm text-gray-600">{transaction.reason}</span>
                                      {transaction.pricing && (
                                        <span className="text-xs text-gray-500">
                                          {transaction.pricing.modelName}
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-right">
                                      <p className={`font-medium ${
                                        transaction.type === TransactionType.Out ? 'text-red-600' : 'text-green-600'
                                      }`}>
                                        {transaction.type === TransactionType.Out ? '-' : '+'}
                                        {formatAmount(Math.abs(transaction.amount))}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {format(new Date(transaction.timestamp), 'MM-dd HH:mm:ss')}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default HierarchicalTransactionView;
