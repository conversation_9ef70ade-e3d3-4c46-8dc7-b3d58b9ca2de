import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Pagination from '@/components/common/Pagination';
import { useTable } from '@/hooks/useTable';
import { useApi } from '@/hooks/useApi';
import { financialService } from '@/services/financial';
import { BalanceTransaction, TransactionListParams, TransactionType } from '@/types/financial';
import { TableColumn } from '@/types/common';

const TransactionList: React.FC = () => {
  const [transactions, setTransactions] = useState<BalanceTransaction[]>([]);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    userId: '',
    type: '',
    dateFrom: '',
    dateTo: '',
    amountMin: '',
    amountMax: '',
    reason: '',
  });

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'timestamp',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchTransactions,
  } = useApi(financialService.getTransactions);

  // 加载交易记录列表
  const loadTransactions = useCallback(async () => {
    try {
      const params: TransactionListParams = {
        ...getQueryParams(),
        ...filters,
      };
      // 清理空值
      Object.keys(params).forEach(key => {
        if (params[key as keyof TransactionListParams] === '') {
          delete params[key as keyof TransactionListParams];
        }
      });
      
      const result = await fetchTransactions(params);
      setTransactions(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载交易记录失败:', error);
    }
  }, [fetchTransactions, getQueryParams, filters]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  // 筛选器变化处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 获取交易类型信息
  const getTransactionTypeInfo = (type: TransactionType) => {
    const typeMap = {
      [TransactionType.Deposit]: { text: '充值', variant: 'success' as const },
      [TransactionType.In]: { text: '入账', variant: 'info' as const },
      [TransactionType.Out]: { text: '消费', variant: 'warning' as const },
    };
    return typeMap[type] || { text: '未知', variant: 'default' as const };
  };

  // 格式化金额显示
  const formatAmount = (amount: number, type: TransactionType) => {
    const prefix = type === TransactionType.Out ? '-' : '+';
    const color = type === TransactionType.Out ? 'text-red-600' : 'text-green-600';
    return (
      <span className={`font-medium ${color}`}>
        {prefix}¥{Math.abs(amount).toFixed(2)}
      </span>
    );
  };

  // 表格列定义
  const columns: TableColumn<BalanceTransaction>[] = [
    {
      key: 'timestamp',
      title: '交易时间',
      dataIndex: 'timestamp',
      width: 150,
      sortable: true,
      render: (timestamp: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(timestamp), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(timestamp), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'user',
      title: '用户信息',
      dataIndex: 'user',
      render: (user: any, record: BalanceTransaction) => (
        <div>
          <p className="text-sm font-medium text-gray-900">
            {user?.fullName || user?.email || record.userId}
          </p>
          {user?.email && user.fullName && (
            <p className="text-xs text-gray-500">{user.email}</p>
          )}
          <p className="text-xs text-gray-400">ID: {record.userId.substring(0, 8)}...</p>
        </div>
      ),
    },
    {
      key: 'type',
      title: '交易类型',
      dataIndex: 'type',
      width: 100,
      render: (type: TransactionType) => {
        const typeInfo = getTransactionTypeInfo(type);
        return <Badge variant={typeInfo.variant}>{typeInfo.text}</Badge>;
      },
    },
    {
      key: 'amount',
      title: '交易金额',
      dataIndex: 'amount',
      width: 120,
      sortable: true,
      render: (amount: number, record: BalanceTransaction) => formatAmount(amount, record.type),
    },
    {
      key: 'reason',
      title: '交易原因',
      dataIndex: 'reason',
      render: (reason: string) => (
        <span className="text-sm text-gray-900" title={reason}>
          {reason.length > 30 ? `${reason.substring(0, 30)}...` : reason}
        </span>
      ),
    },
    {
      key: 'pricing',
      title: '关联模型',
      dataIndex: 'pricing',
      width: 120,
      render: (pricing: any) => (
        pricing ? (
          <div>
            <p className="text-sm text-gray-900">{pricing.modelName}</p>
            <p className="text-xs text-gray-500">
              {pricing.alias?.zh || pricing.alias?.en || ''}
            </p>
          </div>
        ) : (
          <span className="text-sm text-gray-400">-</span>
        )
      ),
    },
    {
      key: 'userBalance',
      title: '用户余额',
      dataIndex: 'user',
      width: 100,
      render: (user: any) => (
        user?.balance !== undefined ? (
          <span className="text-sm font-medium text-gray-900">
            ¥{user.balance.toFixed(2)}
          </span>
        ) : (
          <span className="text-sm text-gray-400">-</span>
        )
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: '_id',
      width: 100,
      render: (id: string, record: BalanceTransaction) => (
        <Button
          size="sm"
          variant="secondary"
          onClick={() => {
            // 这里可以添加查看详情的逻辑
            console.log('查看交易详情:', record);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">交易记录管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看和管理所有用户的交易记录
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => window.open('/admin/financial/transactions/hierarchical', '_blank')}
          >
            分层级视图
          </Button>
          <Button variant="secondary">
            导出数据
          </Button>
          <Button variant="primary">
            查看统计
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <Input
            placeholder="搜索交易原因..."
            value={searching.search}
            onChange={(e) => searching.onSearch(e.target.value)}
            leftIcon={
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            }
          />
          
          <Input
            placeholder="用户ID"
            value={filters.userId}
            onChange={(e) => handleFilterChange('userId', e.target.value)}
          />

          <select
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">所有类型</option>
            <option value={TransactionType.Deposit}>充值</option>
            <option value={TransactionType.In}>入账</option>
            <option value={TransactionType.Out}>消费</option>
          </select>

          <Input
            placeholder="交易原因"
            value={filters.reason}
            onChange={(e) => handleFilterChange('reason', e.target.value)}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            type="date"
            placeholder="开始日期"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
          />

          <Input
            type="date"
            placeholder="结束日期"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
          />

          <Input
            type="number"
            placeholder="最小金额"
            value={filters.amountMin}
            onChange={(e) => handleFilterChange('amountMin', e.target.value)}
          />

          <Input
            type="number"
            placeholder="最大金额"
            value={filters.amountMax}
            onChange={(e) => handleFilterChange('amountMax', e.target.value)}
          />
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总交易数</p>
              <p className="text-2xl font-semibold text-gray-900">{total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">充值交易</p>
              <p className="text-2xl font-semibold text-gray-900">
                {transactions.filter(t => t.type === TransactionType.Deposit).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">消费交易</p>
              <p className="text-2xl font-semibold text-gray-900">
                {transactions.filter(t => t.type === TransactionType.Out).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总金额</p>
              <p className="text-2xl font-semibold text-gray-900">
                ¥{transactions.reduce((sum, t) => sum + Math.abs(t.amount), 0).toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 交易记录表格 */}
      <Table
        columns={columns}
        data={transactions}
        loading={loading}
        emptyText="暂无交易记录"
        rowKey="_id"
      />

      {/* 分页 */}
      <Pagination
        current={pagination.current}
        total={total}
        pageSize={pagination.pageSize}
        onChange={pagination.onChange}
        onPageSizeChange={pagination.onPageSizeChange}
        showSizeChanger
        showQuickJumper
        showTotal
      />
    </div>
  );
};

export default TransactionList;
