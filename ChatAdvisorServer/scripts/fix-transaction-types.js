/**
 * 修复交易记录类型的脚本
 * 
 * 问题：在聊天计费逻辑中，用户输入消息的计费被错误地标记为 TransactionType.In (入账)
 * 实际上应该是 TransactionType.Out (消费)，因为这些记录的 amount 都是负数
 * 
 * 修复策略：
 * 1. 查找所有 type=2 (TransactionType.In) 且 amount<0 的记录
 * 2. 将这些记录的 type 改为 3 (TransactionType.Out)
 * 3. 记录修复的数量和详情
 */

const mongoose = require('mongoose');
const path = require('path');

// 连接数据库
const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/chatadvisor';
        await mongoose.connect(mongoURI);
        console.log('✅ 数据库连接成功');
    } catch (error) {
        console.error('❌ 数据库连接失败:', error);
        process.exit(1);
    }
};

// 定义交易类型枚举
const TransactionType = {
    Deposit: 1,  // 充值
    In: 2,       // 入账
    Out: 3       // 消费
};

// 定义 BalanceTransaction 模型
const BalanceTransactionSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    amount: { type: Number, required: true },
    reason: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
    modelId: { type: mongoose.Schema.Types.ObjectId, ref: 'Pricing' },
    type: { type: Number, required: true }
}, {
    versionKey: false
});

const BalanceTransaction = mongoose.model('BalanceTransaction', BalanceTransactionSchema);

// 分析当前数据状态
const analyzeCurrentData = async () => {
    console.log('\n📊 分析当前交易数据状态...');
    
    const stats = await BalanceTransaction.aggregate([
        {
            $group: {
                _id: '$type',
                count: { $sum: 1 },
                totalAmount: { $sum: '$amount' },
                avgAmount: { $avg: '$amount' },
                minAmount: { $min: '$amount' },
                maxAmount: { $max: '$amount' }
            }
        },
        {
            $sort: { _id: 1 }
        }
    ]);

    console.log('\n当前交易类型分布:');
    stats.forEach(stat => {
        const typeName = stat._id === 1 ? '充值(Deposit)' : 
                        stat._id === 2 ? '入账(In)' : 
                        stat._id === 3 ? '消费(Out)' : '未知';
        console.log(`  ${typeName}: ${stat.count} 笔, 总金额: ${stat.totalAmount.toFixed(4)}, 平均: ${stat.avgAmount.toFixed(4)}, 范围: [${stat.minAmount.toFixed(4)}, ${stat.maxAmount.toFixed(4)}]`);
    });

    // 查找问题数据
    const problemRecords = await BalanceTransaction.find({
        type: TransactionType.In,
        amount: { $lt: 0 }
    }).countDocuments();

    console.log(`\n🔍 发现问题数据: ${problemRecords} 笔 (type=In 但 amount<0)`);

    return problemRecords;
};

// 修复交易类型
const fixTransactionTypes = async () => {
    console.log('\n🔧 开始修复交易类型...');

    // 查找需要修复的记录
    const problemRecords = await BalanceTransaction.find({
        type: TransactionType.In,
        amount: { $lt: 0 },
        reason: 'Chat with OpenAI'  // 只修复聊天相关的记录
    });

    console.log(`找到 ${problemRecords.length} 笔需要修复的记录`);

    if (problemRecords.length === 0) {
        console.log('✅ 没有需要修复的数据');
        return;
    }

    // 显示前几条记录作为示例
    console.log('\n📝 示例记录 (前5条):');
    problemRecords.slice(0, 5).forEach((record, index) => {
        console.log(`  ${index + 1}. ID: ${record._id}, 用户: ${record.userId}, 金额: ${record.amount}, 时间: ${record.timestamp}, 原因: ${record.reason}`);
    });

    // 询问用户确认
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    const confirm = await new Promise((resolve) => {
        rl.question(`\n❓ 确认要修复这 ${problemRecords.length} 笔记录吗？(y/N): `, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
        });
    });

    if (!confirm) {
        console.log('❌ 用户取消操作');
        return;
    }

    // 执行修复
    const result = await BalanceTransaction.updateMany(
        {
            type: TransactionType.In,
            amount: { $lt: 0 },
            reason: 'Chat with OpenAI'
        },
        {
            $set: { type: TransactionType.Out }
        }
    );

    console.log(`\n✅ 修复完成! 更新了 ${result.modifiedCount} 笔记录`);

    // 记录修复日志
    const logEntry = {
        timestamp: new Date(),
        action: 'fix-transaction-types',
        recordsFixed: result.modifiedCount,
        description: '修复了错误标记为入账(In)的消费记录，将其改为消费(Out)类型'
    };

    console.log('\n📋 修复日志:', JSON.stringify(logEntry, null, 2));
};

// 验证修复结果
const verifyFix = async () => {
    console.log('\n🔍 验证修复结果...');

    const remainingProblems = await BalanceTransaction.find({
        type: TransactionType.In,
        amount: { $lt: 0 }
    }).countDocuments();

    if (remainingProblems === 0) {
        console.log('✅ 验证通过: 没有发现剩余的问题数据');
    } else {
        console.log(`⚠️  警告: 仍有 ${remainingProblems} 笔问题数据需要手动检查`);
    }

    // 重新分析数据状态
    await analyzeCurrentData();
};

// 主函数
const main = async () => {
    try {
        console.log('🚀 开始修复交易记录类型...\n');

        await connectDB();

        // 分析当前数据
        const problemCount = await analyzeCurrentData();

        if (problemCount > 0) {
            // 执行修复
            await fixTransactionTypes();

            // 验证修复结果
            await verifyFix();
        }

        console.log('\n🎉 脚本执行完成!');
        
    } catch (error) {
        console.error('❌ 脚本执行失败:', error);
    } finally {
        await mongoose.disconnect();
        console.log('📴 数据库连接已关闭');
        process.exit(0);
    }
};

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = {
    analyzeCurrentData,
    fixTransactionTypes,
    verifyFix
};
